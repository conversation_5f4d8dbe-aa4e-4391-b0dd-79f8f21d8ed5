\documentclass[11pt,a4paper]{moderncv}

\moderncvstyle{classic}
\moderncvcolor{blue}

\usepackage[scale=0.85]{geometry}
\usepackage[utf8]{inputenc}
\usepackage{lmodern}
\usepackage{hyperref}
\usepackage{fontawesome5}

\name{<PERSON>}{<PERSON><PERSON>}
\title{Desarrollador Full Stack}
\address{Bogotá, D.C., Distrito Capital}{Colombia}
\phone{+57 3053908550}
\email{<EMAIL>}
\social[linkedin]{nicolas-pardo-6156a1222}
\social[github]{Nick220505}

\begin{document}
\makecvtitle

\section{Perfil}
Estudiante de Ingeniería de Sistemas (6to semestre) y desarrollador de software apasionado por el desarrollo web, con experiencia en el diseño e implementación de aplicaciones web modernas utilizando mejores prácticas. Competente en desarrollo full-stack con experiencia en Next.js, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> Boot, FastAPI, Django, Node.js, y tecnologías de bases de datos incluyendo PostgreSQL, Oracle DB (PL/SQL), MongoDB y MySQL. Hábil en el diseño de sistemas tipados, escalables e interfaces frontend modernas. Familiarizado con arquitectura por capas, arquitectura hexagonal, arquitectura feature sliced (FSA), y principios SOLID. Experiencia en implementación de control de acceso basado en roles, sistemas multi-tenant y flujos de autenticación seguros. Poseo un nivel de inglés avanzado, permitiéndome colaborar eficazmente en entornos internacionales.\newline
Además, soy un desarrollador autodidacta y la mayor parte de mis conocimientos los he adquirido por mi cuenta.

\section{Experiencia Profesional}
\cventry{Feb 2025--Presente}{Desarrollador Full Stack - Proyecto Insolventic}{Armonia Concertada - Constructores de Paz}{Bogotá, Colombia}{}{\begin{itemize}
\item Frontend: Next.js 15, React 19, Tailwind CSS, componentes Shadcn UI, TypeScript, React Hook Form, UI optimista
\item Backend: Next.js Server Actions, Prisma ORM, PostgreSQL, esquemas de validación Zod
\item Full Stack: Arquitectura Feature Sliced (FSA), diseño type-safe de extremo a extremo, autenticación JWT
\item DevOps: GitHub Actions, despliegue en Vercel, pipelines automatizados CI/CD, separación de entornos
\item Desarrollando un sistema integral de gestión de procesos de insolvencia para profesionales legales en cumplimiento con la ley colombiana. Implementando arquitectura multi-tenant con control de acceso basado en roles y permisos granulares. Construyendo flujo completo de gestión de casos incluyendo perfiles de deudores, gestión de acreedores, programación de audiencias y generación de documentos. Creando dashboards interactivos con analítica en tiempo real y asegurando el manejo seguro de datos siguiendo requisitos legales.
\end{itemize}}

\cventry{Jun 2023--Dic 2024}{Desarrollador Junior Full Stack}{Juana Archila S.A.S}{Bogotá, Colombia}{}{\begin{itemize}
\item Frontend: HTML5, CSS3, TailwindCSS, React, Angular.
\item Backend: Python (Django, FastAPI), JavaScript (Express.js).
\item Full Stack: Next.js, PostgreSQL, MongoDB.
\item DevOps: Docker, GitHub Actions.
\item Juan mostró un compromiso excepcional con el aprendizaje y la mejora continua, adaptándose a nuevas tecnologías y enfocándose en la calidad del software. Posee las habilidades necesarias para desempeñarse con éxito en el rol de Desarrollador Full Stack.
\end{itemize}}

\section{Educación}
\cventry{2023--2027}{Ingeniería de Sistemas (6to semestre)}{Universidad El Bosque}{Bogotá, Colombia}{}{}
\cventry{May 2024--Nov 2024}{Desarrollo Full Stack - Full Stack Open 2024}{Universidad de Helsinki}{En línea}{}{
\begin{itemize}
\item Currículum completo cubriendo desarrollo web moderno: frontend, backend, DevOps y móvil
\item Frontend: React, Redux, React Query, TypeScript, Material-UI, styled-components, React Native
\item Backend: Node.js, Express, APIs REST, GraphQL, Apollo, MongoDB, PostgreSQL, Sequelize ORM
\item DevOps \& Herramientas: Docker, CI/CD con GitHub Actions, Jest, Playwright, Webpack, ESLint
\item Conceptos avanzados: Gestión de estado, autenticación, webhooks, custom hooks, integración TypeScript
\item Repositorio del curso: \href{https://gitfront.io/r/Nick220505/j6RXXGqXsxRU/fullstackopen/}{\faGithub\ \texttt{gitfront.io/r/Nick220505/j6RXXGqXsxRU/fullstackopen}}
\end{itemize}}
\cventry{Jun 2024--Jul 2024}{CS50W: Programación Web con Python y JavaScript}{Universidad de Harvard}{En línea}{}{
\begin{itemize}
\item Desarrollo web avanzado enfocado en aplicaciones full-stack
\item Temas: Seguridad Web, Escalabilidad, Diseño UX, Servicios en la Nube
\item Tecnologías: Django, React, Git, Heroku, Diseño de Bases de Datos y Migraciones
\end{itemize}}
\cventry{Jun 2024--Jul 2024}{CS50SQL: Introducción a Bases de Datos con SQL}{Universidad de Harvard}{En línea}{}{
\begin{itemize}
\item Estudio integral de bases de datos relacionales y dominio de SQL
\item Temas: Modelado de Datos, Normalización de Tablas, Vistas, Índices, Integración de Bases de Datos
\item Tecnologías: SQLite, PostgreSQL, MySQL, Diseño y Optimización de Bases de Datos
\end{itemize}}
\cventry{Jun 2023--Jul 2023}{CS50P: Introducción a la Programación con Python}{Universidad de Harvard}{En línea}{}{
\begin{itemize}
\item Conceptos avanzados de programación Python y programación orientada a objetos
\item Temas: Funciones, Variables, Condicionales, Bucles, Expresiones Regulares
\item Enfoque en pruebas unitarias, E/S de archivos, bibliotecas y manejo de excepciones
\end{itemize}}
\cventry{Abr 2023--Ago 2023}{CS50x: Introducción a las Ciencias de la Computación}{Universidad de Harvard}{En línea}{}{
\begin{itemize}
\item Estudio integral de fundamentos de ciencias de la computación, algoritmos y estructuras de datos
\item Temas: Pensamiento Algorítmico, Gestión de Memoria, Principios de Ingeniería de Software, Línea de Comandos Linux
\item Tecnologías: C, Python, SQL, HTML, CSS, JavaScript, Flask
\end{itemize}}
\cventry{Abr 2023--May 2023}{CS50T: Tecnología para Ciencias de la Computación}{Universidad de Harvard}{En línea}{}{
\begin{itemize}
\item Conceptos fundamentales: Internet, Multimedia, Seguridad, Desarrollo Web
\item Enfoque en fundamentos y principios tecnológicos
\item Tecnologías web: HTML, CSS, Bootstrap
\end{itemize}}

\section{Proyectos Técnicos}
\subsection{Sistema de Gestión de Contratos}
\cventry{May 2024--Presente}{Desarrollador Full Stack}{}{}{}{
\begin{itemize}
\item Desarrollo de un sistema integral de gestión de contratos con control de acceso basado en roles
\item Backend: Python, FastAPI, PostgreSQL, autenticación Keycloak, integración de correo
\item Frontend: Angular 17, TypeScript, Angular Material, RxJS
\item DevOps: Docker, Docker Compose, Nginx
\item Características: Gestión documental, seguimiento de ciclo de vida de contratos, registro de auditoría, informes, formularios dinámicos
\item Implementación de características de seguridad incluyendo HTTPS obligatorio, protección contra vulnerabilidades comunes
\item Creación de documentación completa y elaboración de informes mensuales detallados
\item Desarrollo integral del proyecto incluyendo diseño UI/UX, arquitectura backend y despliegue
\item Contacto de referencia: Miguel Ángel Porras Villarreal, +57 3106197516
\end{itemize}}

\subsection{Breathe Coherence - Plataforma E-commerce Holística}
\cventry{Ene 2025--Presente}{Desarrollador Full Stack}{}{}{}{
\begin{itemize}
\item Desarrollo de plataforma e-commerce moderna para geometría sagrada y esencias curativas
\item Frontend: Next.js 15, React 19, TailwindCSS, Server Components
\item Backend: Prisma ORM, Migraciones de base de datos, APIs RESTful
\item Características: Chatbot con IA usando Gemini API, procesamiento de pagos, gestión de inventario
\item Implementación de diseño responsive, optimización SEO y monitoreo de rendimiento
\item Repositorio del proyecto: \href{https://gitfront.io/r/Nick220505/KNgz9APuLsyo/breathecoherence/}{\faGithub\ \texttt{gitfront.io/r/Nick220505/KNgz9APuLsyo/breathecoherence}}
\item Sitio web: \href{https://www.breathecoherence.com/}{\faGlobe\ \texttt{www.breathecoherence.com}}
\end{itemize}}

\subsection{Entrecol - Sistema de Gestión de Entretenimiento y Empleados}
\cventry{Oct 2024--Nov 2024}{Desarrollador Full Stack}{}{}{}{
\begin{itemize}
\item Desarrollo de aplicación web integral combinando análisis de contenido y gestión de empleados
\item Frontend: Angular 18, TypeScript, Angular Material, NgxCharts, gestión de estado con Signals
\item Backend: Spring Boot 3, Java 21, JPA/Hibernate, MySQL, autenticación JWT
\item DevOps: Docker, Docker Compose, builds multi-etapa, monitoreo de salud
\item Implementación de visualización de datos en tiempo real, generación de informes y autenticación segura
\end{itemize}}

\subsection{Bloglist - Aplicación de Gestión de Blogs}
\cventry{Jun 2024--Jul 2024}{Desarrollador Full Stack}{}{}{}{
\begin{itemize}
\item Desarrollo de sistema full-stack de gestión de blogs con 97\%+ de cobertura de pruebas
\item Frontend: React 18, Material UI, React Query, React Router, Vite
\item Backend: Node.js, Express, MongoDB, autenticación JWT
\item Testing: Playwright (E2E), Vitest, Jest, React Testing Library
\item DevOps: Docker, Nginx, GitHub Actions CI/CD, despliegue en Render.com
\end{itemize}}

\subsection{SIGRAP - Sistema de Gestión de Papelería}
\cventry{Abr 2025--May 2025}{Desarrollador Full Stack}{}{}{}{
\begin{itemize}
\item Desarrollo de sistema integral de gestión para operaciones de papelerías
\item Frontend: Angular 19, TypeScript, PrimeNG, TailwindCSS, NgRx Signals, Chart.js
\item Backend: Spring Boot 3, Java 21, Spring Security, JPA/Hibernate, PostgreSQL
\item DevOps: Docker, Docker Compose, Maven, monitoreo de salud, builds multi-etapa
\item Características: Seguimiento de ventas, gestión de inventario, administración de clientes/proveedores, gestión de empleados, reportes detallados, registro de auditoría, sistema de notificaciones
\item Implementación de autenticación JWT, control de acceso basado en roles y documentación completa de API con OpenAPI/Swagger
\item Construcción de UI responsive con visualización de datos en tiempo real y generación automatizada de reportes
\item Repositorios del proyecto: Backend y Frontend en GitHub con documentación integral
\end{itemize}}

\section{Habilidades Técnicas}
\cvitem{Lenguajes}{Java, TypeScript, JavaScript, Python, C, C\#, SQL, YAML}
\cvitem{Frontend}{Next.js, React, Angular, React Native, HTML5, CSS3, SASS, Material UI, Bootstrap, TailwindCSS, Shadcn UI, Angular Material, PrimeNG, JSF, PrimeFaces, RxJS, Redux}
\cvitem{Backend}{Spring Boot, Spring Security, Node.js, Express, Django, FastAPI, Flask, Prisma ORM, Java JSP, Hibernate, Jakarta Persistence, Sequelize.js, GraphQL}
\cvitem{Bases de Datos}{PostgreSQL, MySQL, Oracle DB, PL/SQL (Procedimientos Almacenados, Funciones, Triggers, Paquetes, Cursores), MongoDB, CockroachDB, Supabase, SQLite, Migraciones, Diseño de BD, Normalización de Tablas, Vistas, Vistas Materializadas, Índices, Particionamiento, Optimización de Consultas, Ajuste de Rendimiento, Gestión de Transacciones, Control de Concurrencia, Prevención de Inyección SQL, Seguridad de Bases de Datos}
\cvitem{IA/Integración}{Gemini API, Desarrollo de Chatbots, Integración de APIs de Terceros, reCAPTCHA}
\cvitem{Testing}{Playwright, Jest, Vitest, JUnit, Desarrollo Guiado por Pruebas, Pruebas Unitarias, de Integración y E2E}
\cvitem{DevOps}{Docker, Orquestación Docker, GitHub Actions, CI/CD, Nginx, Heroku, Vercel, Koyeb, Control de Versiones Git}
\cvitem{Herramientas}{Git, VS Code, Eclipse IDE, PHPMyAdmin, pgAdmin, Angular CLI, JFreeChart, Project Lombok}
\cvitem{Sistemas Operativos}{Linux, Dominio de Interfaz de Línea de Comandos (CLI)}
\cvitem{Conceptos Core}{APIs REST, AJAX, Estructuras de Datos, Algoritmos, POO, Gestión de Memoria, Principios de Ingeniería de Software, Seguridad Web, Escalabilidad, Diseño UX}

\section{Certificaciones}
\cventry{Ene 2024}{C\# Fundamental con Microsoft}{freeCodeCamp}{}{}{}
\cventry{Jun 2024}{Core Fullstack}{Universidad de Helsinki}{}{}{}
\cventry{Jun 2024}{Contenedores}{Universidad de Helsinki}{}{}{}
\cventry{Jun 2024}{TypeScript}{Universidad de Helsinki}{}{}{}
\cventry{Jun 2024}{React Native}{Universidad de Helsinki}{}{}{}
\cventry{Jun 2024}{CI/CD}{Universidad de Helsinki}{}{}{}
\cventry{Jun 2024}{GraphQL}{Universidad de Helsinki}{}{}{}
\cventry{Jun 2024}{Bases de Datos Relacionales}{Universidad de Helsinki}{}{}{}
\cventry{2024}{Certificado CS50W}{Universidad de Harvard}{}{}{}
\cventry{2024}{Certificado CS50SQL}{Universidad de Harvard}{}{}{}
\cventry{2023}{Certificado CS50x}{Universidad de Harvard}{}{}{}
\cventry{2023}{Certificado CS50P}{Universidad de Harvard}{}{}{}
\cventry{2023}{Certificado CS50T}{Universidad de Harvard}{}{}{}

\section{Idiomas}
\cvitem{Inglés}{Nivel avanzado}
\cvitem{Español}{Nativo}

\end{document} 
